import pandas as pd

def score_candidates(df: pd.DataFrame, niche_words):
    if df.empty:
        return df
    text = (df["title"].fillna('') + " " + df["description"].fillna('')).str.lower()
    niche_score = 0
    for w in niche_words:
        niche_score += text.str.contains(w.lower()).astype(int)
    v = df["view_velocity"]
    e = df["engagement_rate"].fillna(0)
    # Normalize features
    v = (v - v.min()) / (v.max() - v.min() + 1e-9)
    e = (e - e.min()) / (e.max() - e.min() + 1e-9)
    nm = (niche_score - niche_score.min()) / (niche_score.max() - niche_score.min() + 1e-9)
    df = df.copy()
    # Tuned for breaking AI/Gaming updates
    df["score"] = 0.40*v + 0.30*e + 0.20*(1.0/ (1.0+df["hours_since"])) + 0.10*nm
    return df.sort_values("score", ascending=False)
