import os, pickle
from dotenv import load_dotenv
from googleapiclient.discovery import build
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from datetime import datetime, timedelta, timezone

load_dotenv()

SCOPES = [
    "https://www.googleapis.com/auth/youtube.upload",
    "https://www.googleapis.com/auth/yt-analytics.readonly",
    "https://www.googleapis.com/auth/youtube.readonly",
]

def yt_client():
    creds = None
    if os.path.exists('token.pickle'):
        with open('token.pickle','rb') as f:
            creds = pickle.load(f)
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(os.getenv("YT_CLIENT_SECRETS","client_secret.json"), SCOPES)
            creds = flow.run_local_server(port=0)
        with open('token.pickle','wb') as f:
            pickle.dump(creds, f)
    return build('youtube', 'v3', credentials=creds)

def schedule_iso_ist(hour_ist: int = 19) -> str:
    ist = timezone(timedelta(hours=5, minutes=30))
    now_ist = datetime.now(ist)
    dt = now_ist.replace(hour=hour_ist, minute=0, second=0, microsecond=0)
    if dt < now_ist:
        dt += timedelta(days=1)
    return dt.astimezone(timezone.utc).isoformat()

def upload_video(video_path, title, description, tags, thumb_path=None, category_id="20", privacy="private") -> str:
    yt = yt_client()
    body = {
        "snippet": {
            "title": title, "description": description, "tags": tags, "categoryId": str(category_id)
        },
        "status": {
            "privacyStatus": privacy,
            "selfDeclaredMadeForKids": False,
            "publishAt": schedule_iso_ist(int(os.getenv("YT_PUBLISH_HOUR_IST","19")))
        }
    }
    req = yt.videos().insert(part="snippet,status", body=body, media_body=video_path)
    res = req.execute()
    vid = res["id"]
    if thumb_path:
        yt.thumbnails().set(videoId=vid, media_body=thumb_path).execute()
    return vid
