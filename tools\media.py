import os, re, pathlib, math, wave, struct
from typing import List
from pydub import AudioSegment
from moviepy.editor import AudioFileClip, TextClip, CompositeVideoClip, ColorClip
from dotenv import load_dotenv
load_dotenv()

OUT_DIR = pathlib.Path(os.getenv("OUT_DIR","./out"))
OUT_DIR.mkdir(parents=True, exist_ok=True)

def _fallback_tts(text: str, out_wav: str):
    try:
        import pyttsx3
        engine = pyttsx3.init()
        engine.save_to_file(text, out_wav)
        engine.runAndWait()
        return out_wav
    except Exception:
        # 3s sine beep placeholder
        sr = 44100; dur = 3
        w = wave.open(out_wav,'w')
        w.setnchannels(1); w.setsampwidth(2); w.setframerate(sr)
        for i in range(sr*dur):
            val = int(32767*0.3*math.sin(2*math.pi*440*i/sr))
            w.writeframes(struct.pack('<h', val))
        w.close()
        return out_wav

def synth_voice(script_md: str) -> str:
    txt = re.sub(r"#.*","", script_md)
    api = os.getenv("ELEVEN_API_KEY")
    out = OUT_DIR / "voice.wav"
    if api:
        try:
            from elevenlabs.client import ElevenLabs
            client = ElevenLabs(api_key=api)
            audio = client.text_to_speech.convert(
                voice_id=os.getenv("ELEVEN_VOICE_ID","21m00Tcm4TlvDq8ikWAM"),
                output_format="wav",
                text=txt,
                model_id="eleven_multilingual_v2"
            )
            with open(out, "wb") as f:
                for chunk in audio:
                    if chunk: f.write(chunk)
            return str(out)
        except Exception:
            pass
    return _fallback_tts(txt, str(out))

def render_video_with_captions(voice_wav: str, captions: List[str], resolution=(1080,1920), fps=30) -> str:
    audio = AudioFileClip(voice_wav)
    W,H = resolution
    bg = ColorClip(size=(W,H), color=(10,10,10), duration=audio.duration)
    clips = [bg.set_audio(audio)]
    n = max(3, min(6, len(captions))) if captions else 4
    beat = audio.duration / n
    for i in range(n):
        t0, t1 = i*beat, (i+1)*beat
        text = captions[i%len(captions)] if captions else f"Beat {i+1}"
        txt = TextClip(txt=text, fontsize=72, font="Arial-Bold", stroke_color="black", stroke_width=2)                .set_duration(t1-t0).set_position("center").set_start(t0)
        clips.append(txt)
    video = CompositeVideoClip(clips)
    out = OUT_DIR / "video.mp4"
    video.write_videofile(str(out), fps=fps, codec="libx264", audio_codec="aac", verbose=False, logger=None)
    return str(out)

def make_thumbnail(title: str) -> str:
    from moviepy.editor import ColorClip, TextClip, CompositeVideoClip
    W,H = 1280, 720
    bg = ColorClip((W,H), color=(0,0,0), duration=2)
    txt = TextClip(txt=title[:48], fontsize=110, font="Arial-Bold", stroke_color="white", stroke_width=3)            .set_position("center").set_duration(2)
    comp = CompositeVideoClip([bg, txt])
    out = OUT_DIR / "thumb.png"
    comp.save_frame(str(out), t=0.1)
    return str(out)
