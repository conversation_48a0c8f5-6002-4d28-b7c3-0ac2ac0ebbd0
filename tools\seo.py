from typing import Dict, List

AI_GAMING_DEFAULT_TAGS = [
    "ai news","gaming news","machine learning","video game updates",
    "ai gaming","game ai","latest ai update","ai podcast","gaming podcast"
]

def build_metadata(topic: str, titles: List[str], sources: List[str]) -> Dict:
    title = (titles[0] if titles else topic)[:95]
    desc_lines = [
        f"Today: {topic}",
        "—",
        "Quick recap: 3 key points for AI & Gaming fans.",
    ]
    if sources:
        desc_lines += ["Sources:"] + [f"- {s}" for s in sources[:5]]
    desc_lines += ["", "Daily AI & gaming updates — subscribe for more!"]
    topic_tags = [w.strip("#,.:;").lower() for w in topic.split() if len(w)>2]
    tags = []
    for t in AI_GAMING_DEFAULT_TAGS + topic_tags:
        if t not in tags and len(tags) < 15:
            tags.append(t)
    return {"title": title, "description": "\n".join(desc_lines), "tags": tags}
