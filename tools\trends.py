import os
from datetime import datetime, timedelta, timezone
from typing import List
import pandas as pd
from googleapiclient.discovery import build
from dotenv import load_dotenv

load_dotenv()

def _yt_client():
    from google_auth_oauthlib.flow import InstalledAppFlow
    from google.auth.transport.requests import Request
    import pickle
    scopes = ["https://www.googleapis.com/auth/youtube.readonly"]
    creds = None
    if os.path.exists('token.pickle'):
        with open('token.pickle','rb') as f:
            creds = pickle.load(f)
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(os.getenv("YT_CLIENT_SECRETS","client_secret.json"), scopes)
            creds = flow.run_local_server(port=0)
        with open('token.pickle','wb') as f:
            pickle.dump(creds, f)
    return build('youtube', 'v3', credentials=creds)

def fetch_candidates(niche_keywords: List[str], region: str = "IN", lookback_hours: int = 48, max_results: int = 40) -> pd.DataFrame:
    yt = _yt_client()
    now = datetime.now(timezone.utc)
    published_after = (now - timedelta(hours=lookback_hours)).isoformat()

    rows = []
    # 1) Region trending
    req = yt.videos().list(part="snippet,statistics,contentDetails", chart="mostPopular", regionCode=region, maxResults=min(max_results,50))
    res = req.execute()
    rows.extend(res.get("items", []))

    # 2) Keyword search
    for kw in niche_keywords:
        req = yt.search().list(part="snippet", q=kw, order="date", publishedAfter=published_after, maxResults=min(max_results,50), type="video")
        res = req.execute()
        for it in res.get("items", []):
            vid = it["id"]["videoId"]
            vres = yt.videos().list(part="snippet,statistics,contentDetails", id=vid).execute()
            rows.extend(vres.get("items", []))

    # Dedup + features
    seen = set(); data = []
    for it in rows:
        vid = it["id"]
        if vid in seen: continue
        seen.add(vid)
        sn, st, cd = it.get("snippet",{}), it.get("statistics",{}), it.get("contentDetails",{})
        views = int(st.get("viewCount", 0) or 0)
        likes = int(st.get("likeCount", 0) or 0)
        comments = int(st.get("commentCount", 0) or 0)
        data.append({
            "video_id": vid,
            "title": sn.get("title",""),
            "channel": sn.get("channelTitle",""),
            "publishedAt": sn.get("publishedAt"),
            "views": views, "likes": likes, "comments": comments,
            "duration": cd.get("duration",""),
            "description": sn.get("description",""),
        })
    df = pd.DataFrame(data)
    if df.empty:
        return df
    df["publishedAt"] = pd.to_datetime(df["publishedAt"], errors="coerce", utc=True)
    df["hours_since"] = (pd.Timestamp.now(tz="UTC") - df["publishedAt"]).dt.total_seconds() / 3600.0
    df["view_velocity"] = df["views"] / df["hours_since"].clip(lower=1.0)
    df["engagement_rate"] = (df["likes"] + df["comments"]) / df["views"].replace(0, 1)
    return df.sort_values("view_velocity", ascending=False).reset_index(drop=True)
