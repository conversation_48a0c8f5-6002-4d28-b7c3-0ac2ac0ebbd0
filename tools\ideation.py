import os, json, pathlib
from typing import List
from dotenv import load_dotenv
load_dotenv()

PROMPTS_DIR = pathlib.Path("prompts")

def _read(path):
    return (PROMPTS_DIR / path).read_text(encoding="utf-8")

def generate_script(topic: str, references: List[str]) -> str:
    apikey = os.getenv("OPENAI_API_KEY")
    if not apikey:
        beats = [
            f"## Hook\nBreaking: {topic}",
            "## Fact 1\nWhat happened in one sentence (source-linked in description).",
            "## Fact 2\nWhy it matters for players, devs, or creators.",
            "## Fact 3\nWhat to watch next: release date, patch, or feature.",
            "## Payoff\nOne-line takeaway.",
            "## CTA\nDaily AI & gaming updates — follow for more.",
        ]
        return "\n\n".join(beats)
    from openai import OpenAI
    client = OpenAI(api_key=apikey)
    system = _read("script_short.md")
    ref_text = "\n".join(f"- {r}" for r in references[:5])
    user = f"Topic: {topic}\nReferences (verify facts, label rumors):\n{ref_text}"
    resp = client.chat.completions.create(
        model=os.getenv("LLM_MODEL","gpt-4o-mini"),
        messages=[{"role":"system","content":system},{"role":"user","content":user}],
        temperature=0.6,
    )
    return resp.choices[0].message.content

def generate_titles(primary_keyword: str) -> list[str]:
    apikey = os.getenv("OPENAI_API_KEY")
    if not apikey:
        return [
            f"{primary_keyword} — Big Update",
            f"Breaking: {primary_keyword}",
            f"{primary_keyword} in 60s",
            f"{primary_keyword}: What’s New",
            f"{primary_keyword} — Must-Know",
            f"{primary_keyword} Explained Fast",
            f"New {primary_keyword} Changes",
            f"{primary_keyword} — Quick Recap",
        ]
    from openai import OpenAI
    client = OpenAI(api_key=apikey)
    prompt = _read("title_ideas.md")
    resp = client.chat.completions.create(
        model=os.getenv("LLM_MODEL","gpt-4o-mini"),
        messages=[{"role":"system","content":prompt},{"role":"user","content":f"Primary keyword: {primary_keyword}"}],
        temperature=0.7,
    )
    txt = resp.choices[0].message.content
    try:
        return json.loads(txt)
    except Exception:
        return [t.strip("- ").strip() for t in txt.splitlines() if t.strip()][:8]
