import os, argparse, pathlib, json
from dotenv import load_dotenv
load_dotenv()

from tools.trends import fetch_candidates
from tools.ranker import score_candidates
from tools.ideation import generate_script, generate_titles
from tools.media import synth_voice, render_video_with_captions, make_thumbnail
from tools.seo import build_metadata
from tools.uploader import upload_video

def split_captions_from_script(md: str):
    lines = [l.strip() for l in md.splitlines() if l.strip() and not l.strip().startswith("#")]
    caps = []
    for l in lines:
        words = l.split()
        cur = []
        for w in words:
            cur.append(w)
            if len(cur) >= 10:
                caps.append(" ".join(cur))
                cur = []
        if cur:
            caps.append(" ".join(cur))
    return caps[:8] or ["New video"]

def main(args):
    pathlib.Path(os.getenv("OUT_DIR","./out")).mkdir(parents=True, exist_ok=True)
    niche = [w.strip() for w in os.getenv("YT_NICHE_KEYWORDS","AI news,gaming news").split(",") if w.strip()]
    region = os.getenv("YT_REGION","IN")

    print("[1/6] Fetching candidates...")
    df = fetch_candidates(niche, region=region, lookback_hours=48, max_results=40)
    if df.empty:
        print("No candidates found. Exiting.")
        return 1

    print("[2/6] Scoring candidates...")
    ranked = score_candidates(df, niche)
    top = ranked.iloc[0]
    topic = top["title"]
    references = [f"https://www.youtube.com/watch?v={top['video_id']}"]

    print("[3/6] Generating script & titles...")
    script_md = generate_script(topic, references)
    titles = generate_titles(topic)

    print("[4/6] Synth voice & render video...")
    voice_wav = synth_voice(script_md)
    captions = split_captions_from_script(script_md)
    video_mp4 = render_video_with_captions(voice_wav, captions)
    thumb_png = make_thumbnail(titles[0] if titles else topic)

    print("[5/6] Build metadata...")
    meta = build_metadata(topic, titles, references)
    print(json.dumps(meta, indent=2))

    if args.dry_run:
        print("\nDRY RUN complete — assets generated locally.")
        print("Video:", video_mp4)
        print("Thumbnail:", thumb_png)
        return 0

    print("[6/6] Upload to YouTube...")
    vid = upload_video(
        video_path=video_mp4,
        title=meta["title"],
        description=meta["description"],
        tags=meta["tags"],
        thumb_path=thumb_png,
        category_id=os.getenv("YT_CATEGORY_ID","20"),
        privacy=os.getenv("YT_DEFAULT_PRIVACY","private")
    )
    print("Uploaded video id:", vid)
    return 0

if __name__ == "__main__":
    p = argparse.ArgumentParser()
    p.add_argument("--dry-run", action="store_true", help="Generate assets but skip upload")
    args = p.parse_args()
    raise SystemExit(main(args))
