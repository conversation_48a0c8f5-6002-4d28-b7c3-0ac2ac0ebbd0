# yt-agent — AI & Gaming News Shorts Agent

Daily agent that:
1) Finds trending AI/gaming topics
2) Writes a 45–60s script
3) Synthesizes voice + renders a vertical video with captions
4) Builds SEO metadata + thumbnail
5) Uploads & schedules to YouTube (19:00 IST by default)

## Quick start
```bash
pip install -r requirements.txt
cp .env.example .env
# Put your Google OAuth client_secret.json in the project root
python agent.py --dry-run   # generate video locally without uploading
python agent.py             # upload & schedule
```

## Notes
- Uses YouTube Data API v3 for discovery and upload.
- Prompts are tuned for AI & Gaming audiences.
- Works offline with templates; add OPENAI/ELEVENLABS keys for higher quality.
